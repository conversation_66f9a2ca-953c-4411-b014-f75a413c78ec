using System.ComponentModel.DataAnnotations;

namespace PSSmartMemo.DTO;

public class ReportUserMemoTemplateDto
{
    public string UserId { get; set; } = string.Empty;
    public string UserName { get; set; } = string.Empty;
    public int MemoTemplateId { get; set; }
    public string MemoTemplateTitle { get; set; } = string.Empty;
    public string MemoTypeTitle { get; set; } = string.Empty;
    public bool IsSelected { get; set; }
}

public class UserMemoTemplateAssignmentDto
{
    [Required(ErrorMessage = "Please select a user")]
    public string SelectedUserId { get; set; } = string.Empty;
    public string SelectedUserName { get; set; } = string.Empty;
    public List<MemoTemplateSelectionDto> MemoTemplates { get; set; } = new();
}

public class MemoTemplateSelectionDto
{
    public int MemoTemplateId { get; set; }
    public string MemoTemplateTitle { get; set; } = string.Empty;
    public string MemoTypeTitle { get; set; } = string.Empty;
    public bool IsSelected { get; set; }
    public bool IsActive { get; set; }
}

public class UserMemoTemplateGridDto
{
    public string UserId { get; set; } = string.Empty;
    public string UserName { get; set; } = string.Empty;
    public List<int> AssignedTemplateIds { get; set; } = new();
    public int AssignedTemplatesCount { get; set; }
}

public class ReportMemoDto
{
    public int MemoId { get; set; }
    public string MemoCode { get; set; } = string.Empty;
    public string MemoTitle { get; set; } = string.Empty;
    public string MemoStatus { get; set; } = string.Empty;
    public DateTime? MemoCreatedDate { get; set; }
    public string MemoCreatedBy { get; set; } = string.Empty;
    public string MemoTemplateTitle { get; set; } = string.Empty;
    public string MemoTypeTitle { get; set; } = string.Empty;
    public string MemoTypeCode { get; set; } = string.Empty;
    public string Department { get; set; } = string.Empty;
    public string Division { get; set; } = string.Empty;
    public string LastAction { get; set; } = string.Empty;
    public DateTime? LastActionDate { get; set; }
    public string CurrentStatus { get; set; } = string.Empty;
}
