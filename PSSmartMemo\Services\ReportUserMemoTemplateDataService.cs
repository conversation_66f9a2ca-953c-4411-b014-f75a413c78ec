namespace PSSmartMemo.Services;

public class ReportUserMemoTemplateDataService(IDbContextFactory<ApplicationDbContext> contextFactory)
{
    public async Task<List<UserDTO>> GetAllActiveUsersAsync()
    {
        await using var dc = await contextFactory.CreateDbContextAsync();
        var users = await (from u in dc.Users
            where u.IsActive == true
            orderby u.Name
            select new UserDTO
            {
                UserId = u.UserId,
                Name = $"{u.UserId} - {u.Name}",
                Email = u.Email,
                IsActive = u.IsActive
            }).ToListAsync();

        return users;
    }

    public async Task<List<MemoTemplateSelectionDto>> GetAllActiveTemplatesAsync()
    {
        await using var dc = await contextFactory.CreateDbContextAsync();
        var templates = await (from mt in dc.MemoTemplates
            join mtype in dc.MemoTypes on mt.MemoTypeId equals mtype.MemoTypeId
            where mt.MemoTemplateIsActive == true &&
                  mt.MemoTemplateIsDel == false &&
                  mt.MemoTemplateStatus == 2 // Published templates only
            orderby mt.MemoTemplateTitle
            select new MemoTemplateSelectionDto
            {
                MemoTemplateId = mt.MemoTemplateId,
                MemoTemplateTitle = mt.MemoTemplateTitle,
                MemoTypeTitle = mtype.MemoTypeName,
                IsActive = mt.MemoTemplateIsActive,
                IsSelected = false
            }).ToListAsync();

        return templates;
    }

    public async Task<UserMemoTemplateAssignmentDto> GetUserTemplateAssignmentAsync(string userId)
    {
        await using var dc = await contextFactory.CreateDbContextAsync();

        // Get user info
        var user = await dc.Users.FirstOrDefaultAsync(u => u.UserId == userId);
        if (user == null)
            throw new ArgumentException("User not found");

        // Get all active templates
        var allTemplates = await GetAllActiveTemplatesAsync();

        // Get user's assigned templates
        var assignedTemplateIds = await (from rut in dc.ReportUsersMemoTemplates
            where rut.UserId == userId
            select rut.MemoTemplateId).ToListAsync();

        // Mark assigned templates as selected
        foreach (var template in allTemplates)
            template.IsSelected = assignedTemplateIds.Contains(template.MemoTemplateId);

        return new UserMemoTemplateAssignmentDto
        {
            SelectedUserId = userId,
            SelectedUserName = user.Name,
            MemoTemplates = allTemplates
        };
    }

    public async Task<string> SaveUserTemplateAssignmentsAsync(string userId, List<int> selectedTemplateIds,
        string modifiedBy)
    {
        await using var dc = await contextFactory.CreateDbContextAsync();
        await using var transaction = await dc.Database.BeginTransactionAsync();

        try
        {
            // Remove existing assignments for this user
            var existingAssignments = await dc.ReportUsersMemoTemplates
                .Where(rut => rut.UserId == userId)
                .ToListAsync();

            dc.ReportUsersMemoTemplates.RemoveRange(existingAssignments);

            // Add new assignments
            foreach (var templateId in selectedTemplateIds)
            {
                var assignment = new ReportUsersMemoTemplate
                {
                    UserId = userId,
                    MemoTemplateId = templateId
                };
                dc.ReportUsersMemoTemplates.Add(assignment);
            }

            await dc.SaveChangesAsync();
            await transaction.CommitAsync();

            return "OK";
        }
        catch (Exception ex)
        {
            await transaction.RollbackAsync();
            return $"Error: {ex.Message}";
        }
    }

    public async Task<List<UserMemoTemplateGridDto>> GetAllUserTemplateAssignmentsAsync()
    {
        await using var dc = await contextFactory.CreateDbContextAsync();

        var userAssignments = await (from u in dc.Users
            where u.IsActive == true
            select new UserMemoTemplateGridDto
            {
                UserId = u.UserId,
                UserName = u.Name,
                AssignedTemplateIds = (from rut in dc.ReportUsersMemoTemplates
                    where rut.UserId == u.UserId
                    select rut.MemoTemplateId).ToList(),
                AssignedTemplatesCount = (from rut in dc.ReportUsersMemoTemplates
                    where rut.UserId == u.UserId
                    select rut).Count()
            }).OrderBy(u => u.UserName).ToListAsync();

        return userAssignments;
    }

    public async Task<List<int>> GetUserAssignedTemplateIdsAsync(string userId)
    {
        await using var dc = await contextFactory.CreateDbContextAsync();

        var templateIds = await (from rut in dc.ReportUsersMemoTemplates
            where rut.UserId == userId
            select rut.MemoTemplateId).ToListAsync();

        return templateIds;
    }
}