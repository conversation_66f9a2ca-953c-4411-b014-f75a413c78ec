@layout PreviewLayout
@page "/memos/{MemoId:int}/preview"
@using PSSmartMemo.Components.Layout
@inject IJSRuntime JS
@inject MemoDataService Service
@inject WorklistDataService WLService
@rendermode InteractiveServer
@inject IWebHostEnvironment WebHostEnvironment
@inject NavigationManager NavigationManager
@inject NavigationManager NavMgr

<div class="" id="printable-content">
    <div class="tmp-preview-header">
        <div style="display: flex; justify-content: space-between; align-items: flex-start; gap: 40px; padding: 10px;">

            <div class="memo-header-info">
                <div>
                    <img src="images/logo.png" style="height: 20px" alt="logo"/>
                </div>

                <span class="memo-code">@_memoObj?.MemoCode</span>
                <h1 class="memo-title">@(_memoObj?.MemoTitle?.ToUpper())</h1>
                <div>

                    <div class="memo-initiated">Initiated By: @_memoObj?.InitiatedBy</div>
                    <div class="memo-initiated">Initiated On: @_memoObj?.MemoCreatedDate?.ToString("d MMM, yyyy")</div>
                </div>

                @*<span class="memo-type">@memoObj?.MemoTypeStr</span>*@

            </div>

            <table style="width:300px">
                <tr>
                    <td style="width:120px"><b>Department:</b>&nbsp;</td><td>@_memoObj?.Department</td>
                </tr>
                <tr>
                    <td><b>Division:</b>&nbsp;</td><td>@_memoObj?.Division</td>
                </tr>
            </table>


        </div>
    </div>

    <div class="">
        @if (_memoObj?.MemoSections != null)
        {
            @foreach (var sec in _memoObj.MemoSections.Where(c => c.MemoSectionIgnored == false))
            {
                <div class="tmp-preview-section-block">
                    <div class="tmp-preview-section-header">
                        <h3 class="tmp-preview-section-title">@sec.MemoSectionTitle</h3>
                    </div>
                    <div class="tmp-preview-section-content">
                        @((MarkupString)sec.MemoSectionContentHtml!)
                    </div>
                </div>
            }
        }
    </div>

    <div>
        @if (_attachments.Any())
        {
            <div class="tmp-preview-section-block">
                <div class="tmp-preview-section-header">
                    <h3 class="tmp-preview-section-title">Attachments</h3>
                </div>
                <table class="approver-table">
                    <thead>
                    <tr>
                        <th>File Name</th>
                        <th>Type</th>
                        <th>Size</th>
                        <th>Description</th>
                        <th class="no-print">Action</th>
                    </tr>
                    </thead>
                    <tbody>
                    @foreach (var file in _attachments)
                    {
                        <tr>
                            <td>@file.Name</td>
                            <td>@file.AttachmentType</td>
                            <td>@FormatFileSize(Convert.ToInt64(file.Size))</td>
                            <td>@file.Description</td>
                            <td class="no-print">
                                <MudLink Href="@file.Path" Target="_blank" Disabled="@(file.Path.StartsWith("http"))">
                                    <MudIcon Icon="@Icons.Material.Filled.Download"/>
                                    Download
                                </MudLink>
                            </td>
                        </tr>
                    }
                    </tbody>
                </table>
            </div>
        }

        <div class="tmp-preview-section-block">
            <div class="tmp-preview-section-header">
                <h3 class="tmp-preview-section-title">Approvers</h3>
            </div>
            <PSSmartMemo.Components.Shared.ApproversPopup MemoId="MemoId"></PSSmartMemo.Components.Shared.ApproversPopup>
        </div>
    </div>

    <div class="action-buttons">
        <button @onclick="Print" class="print-button" title="Print Memo">
            <MudIcon Icon="@Icons.Material.Filled.Print"/>
        </button>

    </div>
</div>

@code {
    [Parameter] public int MemoId { get; set; }
    private MemoDto? _memoObj;
    private string _userId = "";
    [CascadingParameter] public Task<AuthenticationState>? AuthState { get; set; }

    protected override async Task OnParametersSetAsync()
    {
        if (AuthState != null)
        {
            var authState = await AuthState;
            if (authState.User.Identity?.IsAuthenticated ?? false)
            {
                _userId = authState.User.Identity.Name!;
            }
        }

        //var isMyMemo = await Service.IsMyMemo(MemoId, _userId);
        //if (!isMyMemo)
        //{
        //    NavMgr.NavigateTo("/");
        //}

        _memoObj = await Service.GetMemoPreview(MemoId);
        _approvalLogs = await WLService.GetMemoApprovalLogs(MemoId);
        _attachments = await Service.GetMemoAttachments(MemoId);
        //(DivisionName, DepartmentName) = await Service.GetUserDivisionAndDeptByMemoId(MemoId);
    }

    private async Task Print()
    {
        await JS.InvokeVoidAsync("window.print");
    }


    private List<MemoApprovalLogDto> _approvalLogs = new();

    private List<MemoAttachmentDto> _attachments = new();

    private string FormatFileSize(long bytes)
    {
        string[] sizes = ["B", "KB", "MB", "GB", "TB"];
        var order = 0;
        double size = bytes;
        while (size >= 1024 && order < sizes.Length - 1)
        {
            order++;
            size /= 1024;
        }

        return $"{size:0.##} {sizes[order]}";
    }

}