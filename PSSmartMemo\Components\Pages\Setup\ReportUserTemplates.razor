@page "/setup/report-user-templates"
@inject ReportUserMemoTemplateDataService Service
@inject IJSRuntime JS
@attribute [Authorize]
@rendermode InteractiveServer
@using FilterType = Syncfusion.Blazor.DropDowns.FilterType

<SfToast @ref="_toastObj"></SfToast>

<SfBreadcrumb class="mb-2">
    <BreadcrumbItems>
        <BreadcrumbItem IconCss="e-icons e-home" Text="Home" Url="/"></BreadcrumbItem>
        <BreadcrumbItem Text="Setup" Url="/setup"></BreadcrumbItem>
        <BreadcrumbItem Text="Report User Templates" Url=""></BreadcrumbItem>
    </BreadcrumbItems>
</SfBreadcrumb>

<div class="mb-2" style="display: flex; gap: 10px; align-items: center">
    <MudText Typo="Typo.h5">Report User Templates</MudText>
</div>

<div class="row mb-3">
    <div class="col-md-6">
        <SfDropDownList DataSource="@_allUsers"
                        Placeholder="Select User"
                        AllowFiltering="true"
                        FilterType="FilterType.Contains"
                        @bind-Value="_selectedUserId"
                        TValue="string"
                        TItem="UserDTO"
                        FloatLabelType="FloatLabelType.Always">
            <DropDownListFieldSettings Value="@nameof(UserDTO.UserId)"
                                       Text="@nameof(UserDTO.Name)">
            </DropDownListFieldSettings>
            <DropDownListEvents TValue="string" TItem="UserDTO" ValueChange="OnUserChanged"></DropDownListEvents>
        </SfDropDownList>
    </div>
    <div class="col-md-3" style="display:flex;flex-direction:column-reverse">
        <MudButton Variant="Variant.Filled"
                   Color="Color.Primary"
                   OnClick="LoadUserTemplates"
                   Disabled="string.IsNullOrEmpty(_selectedUserId)">
            Load Templates
        </MudButton>
    </div>
</div>

@if (_userTemplateAssignment != null && _userTemplateAssignment.MemoTemplates.Any())
{
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">Memo Templates for: @_userTemplateAssignment.SelectedUserName</h6>
                </div>
                <div class="card-body">
                    <SfGrid @ref="_templatesGrid"
                            DataSource="@_userTemplateAssignment.MemoTemplates"
                            AllowFiltering="true"
                            AllowSorting="true"
                            Height="400px"
                            AllowSelection="true">
                        <GridFilterSettings Type="Syncfusion.Blazor.Grids.FilterType.Excel"></GridFilterSettings>
                        <GridColumns>
                            <GridColumn HeaderText="Select" AutoFit="true" AllowFiltering="false" AllowSorting="false">
                                <Template Context="context">
                                    @{
                                        var template = context as MemoTemplateSelectionDto;
                                    }
                                    @if (template != null)
                                    {
                                        <SfCheckBox @bind-Checked="template.IsSelected"
                                                    TChecked="bool"
                                                    ValueChange="@(args => OnTemplateSelectionChanged(template, args.Checked))">
                                        </SfCheckBox>
                                    }
                                </Template>
                            </GridColumn>
                            <GridColumn Field="@nameof(MemoTemplateSelectionDto.MemoTemplateTitle)"
                                        HeaderText="Memo Template"
                                        AutoFit="true">
                            </GridColumn>
                            <GridColumn Field="@nameof(MemoTemplateSelectionDto.MemoTypeTitle)"
                                        HeaderText="Memo Type"
                                        AutoFit="true">
                            </GridColumn>
                            <GridColumn Field="@nameof(MemoTemplateSelectionDto.IsActive)"
                                        HeaderText="Status"
                                        AutoFit="true">
                                <Template Context="context">
                                    @{
                                        var template = context as MemoTemplateSelectionDto;
                                    }
                                    <span class="badge @(template is { IsActive: true } ? "bg-success" : "bg-secondary")">
                                        @(template is { IsActive: true } ? "Active" : "Inactive")
                                    </span>
                                </Template>
                            </GridColumn>
                        </GridColumns>
                    </SfGrid>
                </div>
                <div class="card-footer">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <small class="text-muted">
                                Selected: @_userTemplateAssignment.MemoTemplates.Count(t => t.IsSelected)
                                of @_userTemplateAssignment.MemoTemplates.Count templates
                            </small>
                        </div>
                        <div>
                            <MudButton Variant="Variant.Outlined"
                                       Color="Color.Secondary"
                                       OnClick="SelectAllTemplates"
                                       Size="Size.Small"
                                       Class="me-2">
                                Select All
                            </MudButton>
                            <MudButton Variant="Variant.Outlined"
                                       Color="Color.Secondary"
                                       OnClick="ClearAllTemplates"
                                       Size="Size.Small"
                                       Class="me-2">
                                Clear All
                            </MudButton>
                            <MudButton Variant="Variant.Filled"
                                       Color="Color.Primary"
                                       OnClick="SaveUserTemplates"
                                       StartIcon="@Icons.Material.Filled.Save">
                                Save Templates
                            </MudButton>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
}

@code {
    [CascadingParameter] public Task<AuthenticationState>? AuthState { get; set; }

    private SfToast? _toastObj;
    private SfGrid<MemoTemplateSelectionDto>? _templatesGrid;

    private string _userId = "";
    private string? _selectedUserId;
    private List<UserDTO> _allUsers = new();
    private UserMemoTemplateAssignmentDto? _userTemplateAssignment;

    protected override async Task OnInitializedAsync()
    {
        if (AuthState != null)
        {
            var authState = await AuthState;
            if (authState.User.Identity?.IsAuthenticated ?? false)
            {
                _userId = authState.User.Identity.Name!;
            }
        }

        await LoadUsers();
    }

    private async Task LoadUsers()
    {
        try
        {
            _allUsers = await Service.GetAllActiveUsersAsync();
        }
        catch (Exception ex)
        {
            await ShowToast("Error loading users: " + ex.Message, "error");
        }
    }

    private void OnUserChanged(ChangeEventArgs<string, UserDTO> args)
    {
        _selectedUserId = args.Value;
        _userTemplateAssignment = null;
        //StateHasChanged();
    }

    private async Task LoadUserTemplates()
    {
        if (string.IsNullOrEmpty(_selectedUserId))
            return;

        try
        {
            _userTemplateAssignment = await Service.GetUserTemplateAssignmentAsync(_selectedUserId);
            StateHasChanged();
        }
        catch (Exception ex)
        {
            await ShowToast("Error loading user templates: " + ex.Message, "error");
        }
    }

    private void OnTemplateSelectionChanged(MemoTemplateSelectionDto template, bool isChecked)
    {
        template.IsSelected = isChecked;
        StateHasChanged();
    }

    private void SelectAllTemplates()
    {
        if (_userTemplateAssignment?.MemoTemplates != null)
        {
            foreach (var template in _userTemplateAssignment.MemoTemplates)
            {
                template.IsSelected = true;
            }

            StateHasChanged();
        }
    }

    private void ClearAllTemplates()
    {
        if (_userTemplateAssignment?.MemoTemplates != null)
        {
            foreach (var template in _userTemplateAssignment.MemoTemplates)
            {
                template.IsSelected = false;
            }

            StateHasChanged();
        }
    }

    private async Task SaveUserTemplates()
    {
        if (_userTemplateAssignment == null || string.IsNullOrEmpty(_selectedUserId))
            return;

        try
        {
            var selectedTemplateIds = _userTemplateAssignment.MemoTemplates
                .Where(t => t.IsSelected)
                .Select(t => t.MemoTemplateId)
                .ToList();

            var result = await Service.SaveUserTemplateAssignmentsAsync(_selectedUserId, selectedTemplateIds, _userId);

            if (result == "OK")
            {
                await ShowToast("User template assignments saved successfully!", "success");
            }
            else
            {
                await ShowToast(result, "error");
            }
        }
        catch (Exception ex)
        {
            await ShowToast("Error saving templates: " + ex.Message, "error");
        }
    }

    private async Task ShowToast(string message, string type)
    {
        if (_toastObj != null)
        {
            var toastModel = new ToastModel
            {
                Title = type == "success" ? "Success" : "Error",
                Content = message,
                CssClass = type == "success" ? "e-toast-success" : "e-toast-danger",
                Icon = type == "success" ? "e-success toast-icons" : "e-error toast-icons",
                ShowCloseButton = true,
                ShowProgressBar = true,
                Timeout = 5000
            };
            await _toastObj.ShowAsync(toastModel);
        }
    }

}