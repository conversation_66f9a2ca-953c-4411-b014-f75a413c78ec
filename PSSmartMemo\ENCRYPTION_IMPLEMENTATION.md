# PSSmartMemo Encryption Implementation

## Overview
This document describes the implementation of encryption for memo content fields in the PSSmartMemo application. The encryption is applied to both `memoSectionContentText` and `memoSectionContentHTML` fields, with backward compatibility for existing unencrypted data.

## Key Features
- **AES-256-CBC Encryption**: Strong encryption using industry-standard algorithms
- **Compression**: Content is compressed using GZip before encryption to reduce storage size
- **Salt-based Security**: Each memo uses its MemoId as salt for unique encryption keys
- **Backward Compatibility**: Existing unencrypted data remains accessible
- **Transparent Operation**: Encryption/decryption happens automatically in the data layer

## Implementation Details

### 1. EncryptionService (`PSSmartMemo/Services/EncryptionService.cs`)
- **Purpose**: Handles all encryption and decryption operations
- **Key Features**:
  - Hardcoded master key: `PSSmartMemo2024!@#$%^&*()_+{}|:<>?[]\\;',./`~`
  - Salt generation based on MemoId for unique encryption per memo
  - PBKDF2 key derivation with 10,000 iterations
  - GZip compression before encryption
  - Random IV generation for each encryption operation

**Key Methods**:
- `EncryptAndCompress(string plainText, int memoId)`: Encrypts and compresses content
- `DecryptAndDecompress(string encryptedText, int memoId)`: Decrypts and decompresses content

### 2. MemoSectionDto Updates (`PSSmartMemo/DTO/MemoSectionDto.cs`)
- **Enhanced Properties**:
  - `ContentHtml`: Automatically handles encryption/decryption
  - `Content`: Automatically handles encryption/decryption for text content
  - `MemoIsEncrypted`: Flag indicating encryption status (default: true for new memos)

**Key Methods**:
- `SetEncryptedContent()`: Sets encrypted HTML content from database
- `SetEncryptedTextContent()`: Sets encrypted text content from database
- `GetContentForStorage()`: Returns HTML content ready for database storage
- `GetTextContentForStorage()`: Returns text content ready for database storage

### 3. MemoDataService Updates (`PSSmartMemo/Services/MemoDataService.cs`)
**Save Operations**:
- `SaveMemoSections()`: Updated to encrypt content before saving
- Sets `MemoIsEncrypted = true` for new memo sections
- Uses `GetContentForStorage()` and `GetTextContentForStorage()` methods

**Load Operations**:
- `GetMemoSections()`: Updated to handle encrypted content loading
- `GetMemoPreview()`: Updated to decrypt content for preview display
- Duplicate memo functionality updated to re-encrypt content with new MemoId

### 4. ReportMemoDataService Updates (`PSSmartMemo/Services/ReportMemoDataService.cs`)
- `GetMemoDetailAsync()`: Updated to handle encrypted content for reports
- Uses `SetEncryptedContent()` and `SetEncryptedTextContent()` methods

### 5. Database Schema Considerations
The existing `MemoIsEncrypted` field in the `MemoSection` table is used to indicate encryption status:
- `true`: Content is encrypted and compressed
- `false`: Content is stored as plain text (backward compatibility)

## Security Features

### Encryption Specifications
- **Algorithm**: AES-256-CBC
- **Key Derivation**: PBKDF2 with SHA-256, 10,000 iterations
- **Salt**: Generated from MemoId using SHA-256 hash
- **IV**: Random 16-byte IV generated for each encryption
- **Compression**: GZip compression before encryption

### Key Management
- **Master Key**: Hardcoded in application (should be moved to secure configuration in production)
- **Per-Memo Keys**: Derived from master key + MemoId salt
- **Salt Generation**: Consistent salt based on MemoId ensures same memo always uses same key

## Backward Compatibility

### Existing Data
- Old unencrypted memos continue to work without modification
- `MemoIsEncrypted = false` indicates unencrypted content
- No data migration required

### New Data
- All new memos are encrypted by default (`MemoIsEncrypted = true`)
- Content is automatically encrypted when saving
- Content is automatically decrypted when loading

## Testing

### Test Endpoints
- `GET /api/test/encryption`: Tests basic encryption functionality
- `GET /api/test/memo-section-dto`: Tests MemoSectionDto encryption handling

### Test Coverage
- Basic encryption/decryption
- Different MemoIds produce different encryption
- Empty/null content handling
- Large content compression
- Special character support
- Backward compatibility
- MemoSectionDto integration

## Pages Affected

### Viewing Pages (No Changes Required)
- `Preview.razor`: Uses GetMemoPreview (updated to decrypt)
- `WatchListViewMemo.razor`: Uses GetMemoPreview (updated to decrypt)
- `ViewMemo.razor`: Uses GetMemoPreview (updated to decrypt)
- `MemoDetail.razor`: Uses GetMemoDetailAsync (updated to decrypt)

### Editing Pages
- `MyMemos.razor`: New memos automatically encrypted (MemoIsEncrypted = true by default)

## Performance Considerations

### Compression Benefits
- Repetitive content compresses significantly
- Reduces storage requirements
- Network transfer optimization

### Encryption Overhead
- Minimal performance impact for typical memo sizes
- PBKDF2 key derivation adds security with acceptable performance cost
- Caching of derived keys could be implemented for high-volume scenarios

## Security Recommendations for Production

1. **Key Management**: Move hardcoded key to secure configuration (Azure Key Vault, etc.)
2. **Key Rotation**: Implement key rotation strategy
3. **Audit Logging**: Add encryption/decryption audit trails
4. **Access Control**: Ensure proper authorization for encrypted content access
5. **Backup Security**: Ensure encrypted backups maintain security

## Migration Strategy

### Phase 1: Implementation (Current)
- New memos encrypted automatically
- Existing memos remain unencrypted
- Full backward compatibility

### Phase 2: Optional Migration (Future)
- Background job to encrypt existing unencrypted memos
- Update `MemoIsEncrypted` flag after successful encryption
- Maintain rollback capability

## Error Handling

### Encryption Failures
- Fallback to plain text storage if encryption fails
- Logging of encryption errors
- Graceful degradation

### Decryption Failures
- Fallback to displaying encrypted content as-is
- Error logging for investigation
- User notification of potential data issues

## Conclusion

The encryption implementation provides strong security for memo content while maintaining full backward compatibility and transparent operation. The system automatically handles encryption for new content and seamlessly decrypts existing encrypted content, ensuring a smooth user experience with enhanced data protection.
