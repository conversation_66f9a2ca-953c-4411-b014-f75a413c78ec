using PSSmartMemo.Services;
using PSSmartMemo.DTO;

namespace PSSmartMemo.Tests;

/// <summary>
/// Simple test class to verify encryption functionality
/// This can be called from a controller or service to test encryption
/// </summary>
public static class TestEncryption
{
    public static string RunEncryptionTests()
    {
        var results = new List<string>();
        
        try
        {
            // Test 1: Basic encryption/decryption
            var originalText = "<p>This is a test memo content with <strong>HTML</strong> formatting.</p>";
            var memoId = 12345;
            
            var encrypted = EncryptionService.EncryptAndCompress(originalText, memoId);
            var decrypted = EncryptionService.DecryptAndDecompress(encrypted, memoId);
            
            if (originalText == decrypted && originalText != encrypted)
            {
                results.Add("✓ Test 1 PASSED: Basic encryption/decryption works");
            }
            else
            {
                results.Add("✗ Test 1 FAILED: Basic encryption/decryption failed");
            }
            
            // Test 2: Different memo IDs produce different encryption
            var memoId1 = 123;
            var memoId2 = 456;
            var testText = "<p>Test content</p>";
            
            var encrypted1 = EncryptionService.EncryptAndCompress(testText, memoId1);
            var encrypted2 = EncryptionService.EncryptAndCompress(testText, memoId2);
            
            if (encrypted1 != encrypted2)
            {
                results.Add("✓ Test 2 PASSED: Different memo IDs produce different encryption");
            }
            else
            {
                results.Add("✗ Test 2 FAILED: Different memo IDs should produce different encryption");
            }
            
            // Test 3: MemoSectionDto encryption handling
            var memoSection = new MemoSectionDto
            {
                MemoId = 789,
                MemoIsEncrypted = true
            };
            
            var htmlContent = "<p>Test DTO content with special chars: àáâã</p>";
            memoSection.ContentHtml = htmlContent;
            
            var storageContent = memoSection.GetContentForStorage();
            
            if (storageContent != htmlContent && !string.IsNullOrEmpty(storageContent))
            {
                results.Add("✓ Test 3 PASSED: MemoSectionDto encrypts content for storage");
            }
            else
            {
                results.Add("✗ Test 3 FAILED: MemoSectionDto should encrypt content for storage");
            }
            
            // Test 4: MemoSectionDto decryption handling
            var newMemoSection = new MemoSectionDto { MemoId = 789 };
            newMemoSection.SetEncryptedContent(storageContent, true);
            
            if (newMemoSection.ContentHtml == htmlContent)
            {
                results.Add("✓ Test 4 PASSED: MemoSectionDto decrypts content correctly");
            }
            else
            {
                results.Add("✗ Test 4 FAILED: MemoSectionDto should decrypt content correctly");
            }
            
            // Test 5: Backward compatibility with unencrypted content
            var oldMemoSection = new MemoSectionDto { MemoId = 999 };
            var plainContent = "<p>Old unencrypted content</p>";
            oldMemoSection.SetEncryptedContent(plainContent, false);
            
            if (oldMemoSection.ContentHtml == plainContent)
            {
                results.Add("✓ Test 5 PASSED: Backward compatibility with unencrypted content works");
            }
            else
            {
                results.Add("✗ Test 5 FAILED: Backward compatibility with unencrypted content failed");
            }
            
            // Test 6: Empty/null content handling
            var emptyEncrypted = EncryptionService.EncryptAndCompress("", 123);
            var nullEncrypted = EncryptionService.EncryptAndCompress(null, 123);
            
            if (emptyEncrypted == "" && nullEncrypted == "")
            {
                results.Add("✓ Test 6 PASSED: Empty/null content handling works");
            }
            else
            {
                results.Add("✗ Test 6 FAILED: Empty/null content handling failed");
            }
            
        }
        catch (Exception ex)
        {
            results.Add($"✗ EXCEPTION: {ex.Message}");
        }
        
        return string.Join("\n", results);
    }
    
    public static string TestLargeContent()
    {
        try
        {
            // Test with large content to verify compression
            var largeContent = string.Join("", Enumerable.Repeat("<p>This is a large content block with lots of repetitive text to test compression and encryption. ", 100));
            var memoId = 555;
            
            var encrypted = EncryptionService.EncryptAndCompress(largeContent, memoId);
            var decrypted = EncryptionService.DecryptAndDecompress(encrypted, memoId);
            
            if (decrypted == largeContent && encrypted.Length < largeContent.Length)
            {
                return $"✓ Large content test PASSED: Original: {largeContent.Length} chars, Encrypted: {encrypted.Length} chars (compression ratio: {(double)encrypted.Length / largeContent.Length:P})";
            }
            else
            {
                return "✗ Large content test FAILED";
            }
        }
        catch (Exception ex)
        {
            return $"✗ Large content test EXCEPTION: {ex.Message}";
        }
    }
}
