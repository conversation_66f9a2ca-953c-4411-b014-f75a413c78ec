using Microsoft.AspNetCore.Mvc;
using PSSmartMemo.Tests;

namespace PSSmartMemo.Controllers;

[ApiController]
[Route("api/[controller]")]
public class TestController : ControllerBase
{
    /// <summary>
    /// Test endpoint to verify encryption functionality
    /// </summary>
    [HttpGet("encryption")]
    public IActionResult TestEncryption()
    {
        try
        {
            var results = PSSmartMemo.Tests.TestEncryption.RunEncryptionTests();
            var largeContentTest = PSSmartMemo.Tests.TestEncryption.TestLargeContent();
            
            var response = new
            {
                Status = "Success",
                Message = "Encryption tests completed",
                Results = results,
                LargeContentTest = largeContentTest,
                Timestamp = DateTime.Now
            };
            
            return Ok(response);
        }
        catch (Exception ex)
        {
            return BadRequest(new
            {
                Status = "Error",
                Message = ex.Message,
                Timestamp = DateTime.Now
            });
        }
    }
    
    /// <summary>
    /// Test endpoint to verify MemoSectionDto encryption handling
    /// </summary>
    [HttpGet("memo-section-dto")]
    public IActionResult TestMemoSectionDto()
    {
        try
        {
            var results = new List<string>();
            
            // Test 1: New MemoSectionDto should have encryption enabled by default
            var newSection = new PSSmartMemo.DTO.MemoSectionDto();
            results.Add($"New MemoSectionDto encryption enabled: {newSection.MemoIsEncrypted}");
            
            // Test 2: Test content encryption/decryption
            var section = new PSSmartMemo.DTO.MemoSectionDto
            {
                MemoId = 12345,
                MemoIsEncrypted = true
            };
            
            var originalContent = "<p>Test content with <strong>HTML</strong> and special chars: àáâã</p>";
            section.ContentHtml = originalContent;
            
            var encryptedForStorage = section.GetContentForStorage();
            results.Add($"Content encrypted for storage: {encryptedForStorage != originalContent}");
            results.Add($"Encrypted content length: {encryptedForStorage?.Length ?? 0}");
            
            // Test 3: Test decryption
            var newSection2 = new PSSmartMemo.DTO.MemoSectionDto { MemoId = 12345 };
            newSection2.SetEncryptedContent(encryptedForStorage, true);
            
            var decryptedContent = newSection2.ContentHtml;
            results.Add($"Content decrypted correctly: {decryptedContent == originalContent}");
            
            // Test 4: Test backward compatibility
            var oldSection = new PSSmartMemo.DTO.MemoSectionDto { MemoId = 999 };
            var plainContent = "<p>Old unencrypted content</p>";
            oldSection.SetEncryptedContent(plainContent, false);
            
            results.Add($"Backward compatibility works: {oldSection.ContentHtml == plainContent}");
            
            var response = new
            {
                Status = "Success",
                Message = "MemoSectionDto tests completed",
                Results = results,
                Timestamp = DateTime.Now
            };
            
            return Ok(response);
        }
        catch (Exception ex)
        {
            return BadRequest(new
            {
                Status = "Error",
                Message = ex.Message,
                StackTrace = ex.StackTrace,
                Timestamp = DateTime.Now
            });
        }
    }
}
