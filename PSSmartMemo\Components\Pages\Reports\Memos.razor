@page "/reports/memos"
@inject ReportMemoDataService Service
@inject NavigationManager NavMgr
@attribute [Authorize]
@rendermode InteractiveServer
@using FilterType = Syncfusion.Blazor.Grids.FilterType

<SfBreadcrumb class="mb-2">
    <BreadcrumbItems>
        <BreadcrumbItem IconCss="e-icons e-home" Text="Home" Url="/"></BreadcrumbItem>
        <BreadcrumbItem Text="Reports" Url="/reports"></BreadcrumbItem>
        <BreadcrumbItem Text="Memos" Url=""></BreadcrumbItem>
    </BreadcrumbItems>
</SfBreadcrumb>

<div class="mb-2" style="display: flex; gap: 10px; align-items: center">
    <MudText Typo="Typo.h5">My Assigned Memos</MudText>
    <MudButton Size="Size.Small"
               Variant="Variant.Outlined"
               Color="Color.Primary"
               OnClick="RefreshMemos"
               StartIcon="@Icons.Material.Filled.Refresh">
        Refresh
    </MudButton>
</div>

@if (assignedTemplates.Any())
{
    <div class="row mb-3">
        <div class="col-md-4">
            <SfDropDownList DataSource="@assignedTemplates"
                            Placeholder="Filter by Template (All)"
                            AllowFiltering="true"
                            FilterType="Syncfusion.Blazor.DropDowns.FilterType.Contains"
                            @bind-Value="selectedTemplateId"
                            TValue="int?"
                            TItem="MemoTemplateSelectionDto"
                            FloatLabelType="FloatLabelType.Always">
                <DropDownListFieldSettings Value="@nameof(MemoTemplateSelectionDto.MemoTemplateId)"
                                           Text="@nameof(MemoTemplateSelectionDto.MemoTemplateTitle)">
                </DropDownListFieldSettings>
                <DropDownListEvents TValue="int?" TItem="MemoTemplateSelectionDto" ValueChange="OnTemplateFilterChanged"></DropDownListEvents>
            </SfDropDownList>
        </div>
        <div class="col-md-2" style="display:flex; flex-direction: column-reverse;">

            <MudButton Variant="Variant.Filled"
                       Color="Color.Primary"
                       OnClick="FilterMemosByTemplate"
                       Disabled="selectedTemplateId == null">
                Filter
            </MudButton>
        </div>
        <div class="col-md-2" style="display:flex; flex-direction: column-reverse;">
            <MudButton Variant="Variant.Outlined"
                       Color="Color.Secondary"
                       OnClick="ClearTemplateFilter">
                Show All
            </MudButton>
        </div>
    </div>
}

<div class="row">
    <div class="col-md-12">
        @if (isLoading)
        {
            <div class="text-center p-4">
                <MudProgressCircular Indeterminate="true"/>
                <p class="mt-2">Loading memos...</p>
            </div>
        }
        else if (!memos.Any())
        {
            <div class="alert alert-info">
                <h6>No Memos Found</h6>
                <p class="mb-0">
                    @if (!assignedTemplates.Any())
                    {
                        <text>You don't have any memo templates assigned. Please contact your administrator to assign memo templates to your account.</text>
                    }
                    else
                    {
                        <text>No memos found for your assigned templates.</text>
                    }
                </p>
            </div>
        }
        else
        {
            <SfGrid DataSource="@memos"
                    AllowFiltering="true"
                    AllowSorting="true"
                    Height="calc(100vh - 250px)"
                    AllowTextWrap="true"
                    AllowPaging="true">
                <GridPageSettings PageSize="50"></GridPageSettings>
                <GridFilterSettings Type="FilterType.Excel"></GridFilterSettings>
                <GridColumns>
                    <GridColumn HeaderText="Code" AutoFit="true" AllowFiltering="true">
                        <Template Context="context">
                            @{
                                var memo = context as ReportMemoDto;
                            }
                            <SfButton CssClass="e-link" OnClick="@(() => ViewMemoDetail(memo.MemoId))">
                                @memo.MemoCode
                            </SfButton>
                        </Template>
                    </GridColumn>
                    <GridColumn HeaderText="Preview" AutoFit="true" AllowFiltering="false" AllowSorting="false">
                        <Template Context="context">
                            @{
                                var memo = context as ReportMemoDto;
                            }
                            <a href="@($"/memos/{memo.MemoId}/preview")"
                               target="memo-@(memo.MemoId)"
                               class="mud-link"
                               style="color: #0d6efd; text-decoration: none; font-weight: bold;">
                                <MudIcon Size="Size.Small" Icon="@Icons.Material.Filled.Print"></MudIcon>
                            </a>
                        </Template>
                    </GridColumn>
                    <GridColumn Field="@nameof(ReportMemoDto.MemoTitle)"
                                HeaderText="Title"
                                AutoFit="true">
                    </GridColumn>
                    <GridColumn Field="@nameof(ReportMemoDto.MemoTemplateTitle)"
                                HeaderText="Template"
                                AutoFit="true">
                    </GridColumn>
                    <GridColumn Field="@nameof(ReportMemoDto.MemoTypeCode)"
                                HeaderText="Type"
                                AutoFit="true">
                    </GridColumn>
                    <GridColumn Field="@nameof(ReportMemoDto.CurrentStatus)"
                                HeaderText="Status"
                                AutoFit="true">
                        <Template Context="context">
                            @{
                                var memo = context as ReportMemoDto;
                            }
                            <span class="badge @GetStatusBadgeClass(memo.CurrentStatus)">
                                @memo.CurrentStatus
                            </span>
                        </Template>
                    </GridColumn>
                    <GridColumn Field="@nameof(ReportMemoDto.Department)"
                                HeaderText="Department"
                                AutoFit="true">
                    </GridColumn>
                    <GridColumn Field="@nameof(ReportMemoDto.Division)"
                                HeaderText="Division"
                                AutoFit="true">
                    </GridColumn>
                    <GridColumn Field="@nameof(ReportMemoDto.MemoCreatedBy)"
                                HeaderText="Created By"
                                AutoFit="true">
                    </GridColumn>
                    <GridColumn Field="@nameof(ReportMemoDto.MemoCreatedDate)"
                                HeaderText="Created Date"
                                AutoFit="true"
                                Format="dd/MM/yyyy HH:mm">
                    </GridColumn>
                    <GridColumn Field="@nameof(ReportMemoDto.LastActionDate)"
                                HeaderText="Last Action"
                                AutoFit="true"
                                Format="dd/MM/yyyy HH:mm">
                    </GridColumn>
                </GridColumns>
            </SfGrid>
        }
    </div>
</div>

@code {
    [CascadingParameter] public Task<AuthenticationState>? AuthState { get; set; }

    private string userId = "";
    private bool isLoading = true;
    private List<ReportMemoDto> memos = new();
    private List<ReportMemoDto> allMemos = new();
    private List<MemoTemplateSelectionDto> assignedTemplates = new();
    private int? selectedTemplateId;

    protected override async Task OnInitializedAsync()
    {
        if (AuthState != null)
        {
            var authState = await AuthState;
            if (authState.User.Identity?.IsAuthenticated ?? false)
            {
                userId = authState.User.Identity.Name!;
            }
            else
            {
                NavMgr.NavigateTo("/");
                return;
            }
        }

        await LoadData();
    }

    private async Task LoadData()
    {
        isLoading = true;
        StateHasChanged();

        try
        {
            // Load user's assigned templates
            assignedTemplates = await Service.GetUserAssignedTemplatesAsync(userId);

            // Load all memos for assigned templates
            allMemos = await Service.GetUserAssignedMemosAsync(userId);
            memos = allMemos;
        }
        catch (Exception ex)
        {
            // Handle error - could show toast or error message
            Console.WriteLine($"Error loading data: {ex.Message}");
        }
        finally
        {
            isLoading = false;
            StateHasChanged();
        }
    }

    private async Task RefreshMemos()
    {
        await LoadData();
    }

    private async Task OnTemplateFilterChanged(ChangeEventArgs<int?, MemoTemplateSelectionDto> args)
    {
        selectedTemplateId = args.Value;
    }

    private async Task FilterMemosByTemplate()
    {
        if (selectedTemplateId == null)
            return;

        isLoading = true;
        StateHasChanged();

        try
        {
            memos = await Service.GetUserAssignedMemosByTemplateAsync(userId, selectedTemplateId.Value);
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error filtering memos: {ex.Message}");
        }
        finally
        {
            isLoading = false;
            StateHasChanged();
        }
    }

    private void ClearTemplateFilter()
    {
        selectedTemplateId = null;
        memos = allMemos;
        StateHasChanged();
    }

    private void ViewMemoDetail(int memoId)
    {
        NavMgr.NavigateTo($"/reports/memos/{memoId}/detail");
    }

    private string GetStatusBadgeClass(string status)
    {
        return status?.ToLower() switch
        {
            "approved" => "bg-success",
            "pending" => "bg-warning",
            "rejected" => "bg-danger",
            "draft" => "bg-secondary",
            _ => "bg-info"
        };
    }

}