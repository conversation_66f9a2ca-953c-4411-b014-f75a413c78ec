/* Print-Optimized Stylesheet for Memo Preview */

/* Page setup for print */
@media print {
    @page {
        margin: 0.75in;
        size: A4 portrait;
    }

    /* Global print resets */
    * {
        -webkit-print-color-adjust: exact !important;
        print-color-adjust: exact !important;
    }

    body {
        font-family: 'Times New Roman', serif !important;
        font-size: 12pt !important;
        line-height: 1.4 !important;
        color: #000 !important;
        background: white !important;
        margin: 0 !important;
        padding: 0 !important;
    }

    /* Hide non-printable elements */
    .no-print,
    .action-buttons,
    .mud-button,
    .mud-icon,
    .navigation-controls,
    .print-button {
        display: none !important;
    }

    /* Container optimizations */
    .print-optimized-container {
        width: 100% !important;
        max-width: none !important;
        margin: 0 !important;
        padding: 0 !important;
        box-shadow: none !important;
        border: none !important;
        background: white !important;
    }

    /* Header section - always keep together */
    .print-header-section {
        page-break-inside: avoid !important;
        page-break-after: avoid !important;
        border-bottom: 2pt solid #000 !important;
        margin-bottom: 12pt !important;
        padding-bottom: 12pt !important;
    }

    .header-content {
        display: table !important;
        width: 100% !important;
        table-layout: fixed !important;
    }

    .memo-header-info {
        display: table-cell !important;
        width: 65% !important;
        vertical-align: top !important;
        padding-right: 20pt !important;
    }

    .memo-details-table {
        display: table-cell !important;
        width: 35% !important;
        vertical-align: top !important;
    }

    /* Typography for print */
    .memo-code {
        font-size: 10pt !important;
        color: #000 !important;
        font-weight: normal !important;
        margin-bottom: 6pt !important;
    }

    .memo-title {
        font-size: 16pt !important;
        font-weight: bold !important;
        color: #000 !important;
        margin: 6pt 0 !important;
        text-transform: uppercase !important;
    }

    .memo-initiated {
        font-size: 9pt !important;
        color: #000 !important;
        font-style: italic !important;
        margin-bottom: 2pt !important;
    }

    /* Details table */
    .memo-details-table table {
        width: 100% !important;
        border-collapse: collapse !important;
        font-size: 9pt !important;
    }

    .memo-details-table td {
        padding: 2pt 4pt !important;
        border: 1pt solid #000 !important;
        color: #000 !important;
        vertical-align: top !important;
    }

    .memo-details-table td:first-child {
        font-weight: bold !important;
        background: #f0f0f0 !important;
        width: 40% !important;
    }

    /* Content sections */
    .print-content-sections {
        margin-top: 0 !important;
    }

    .print-section-block {
        page-break-inside: avoid !important;
        margin-bottom: 12pt !important;
        border: 1pt solid #000 !important;
        border-radius: 0 !important;
    }

    /* Prevent orphaned section headers */
    .print-section-header {
        page-break-inside: avoid !important;
        page-break-after: avoid !important;
        background: #f0f0f0 !important;
        border-bottom: 1pt solid #000 !important;
        padding: 6pt 8pt !important;
    }

    .print-section-title {
        font-size: 12pt !important;
        font-weight: bold !important;
        color: #000 !important;
        margin: 0 !important;
        text-transform: capitalize !important;
    }

    .print-section-content {
        padding: 8pt !important;
        color: #000 !important;
        font-size: 10pt !important;
        line-height: 1.3 !important;
    }

    /* Content typography */
    .print-section-content h1,
    .print-section-content h2,
    .print-section-content h3,
    .print-section-content h4,
    .print-section-content h5,
    .print-section-content h6 {
        color: #000 !important;
        page-break-after: avoid !important;
        margin: 6pt 0 3pt 0 !important;
        font-weight: bold !important;
    }

    .print-section-content h1 { font-size: 14pt !important; }
    .print-section-content h2 { font-size: 13pt !important; }
    .print-section-content h3 { font-size: 12pt !important; }
    .print-section-content h4 { font-size: 11pt !important; }
    .print-section-content h5 { font-size: 10pt !important; }
    .print-section-content h6 { font-size: 10pt !important; }

    .print-section-content p {
        margin: 3pt 0 !important;
        orphans: 2 !important;
        widows: 2 !important;
        text-align: justify !important;
    }

    .print-section-content ul,
    .print-section-content ol {
        margin: 3pt 0 !important;
        padding-left: 12pt !important;
    }

    .print-section-content li {
        margin: 1pt 0 !important;
    }

    /* Tables */
    .print-attachments-table {
        width: 100% !important;
        border-collapse: collapse !important;
        font-size: 9pt !important;
        page-break-inside: auto !important;
    }

    .print-attachments-table th,
    .print-attachments-table td {
        padding: 3pt 4pt !important;
        border: 1pt solid #000 !important;
        color: #000 !important;
        text-align: left !important;
    }

    .print-attachments-table th {
        background: #f0f0f0 !important;
        font-weight: bold !important;
        page-break-after: avoid !important;
    }

    .print-attachments-table tr {
        page-break-inside: avoid !important;
    }

    /* Approvers section specific styling */
    .print-approvers-section {
        page-break-inside: avoid !important;
    }

    .print-approvers-section .approvers-container {
        padding: 0 !important;
    }

    .print-approvers-section .approval-grid {
        display: block !important;
        column-count: 3 !important;
        column-gap: 8pt !important;
        column-fill: balance !important;
    }

    .print-approvers-section .approval-box {
        display: inline-block !important;
        width: 100% !important;
        margin: 0 0 6pt 0 !important;
        padding: 4pt !important;
        border: 1pt solid #000 !important;
        text-align: center !important;
        page-break-inside: avoid !important;
        break-inside: avoid !important;
        background: white !important;
    }

    .print-approvers-section .approver-name {
        font-weight: bold !important;
        font-size: 9pt !important;
        color: #000 !important;
        margin-bottom: 2pt !important;
    }

    .print-approvers-section .approver-role {
        font-size: 8pt !important;
        color: #000 !important;
        margin-bottom: 4pt !important;
    }

    .print-approvers-section .approval-line {
        border-top: 1pt solid #000 !important;
        margin: 3pt 0 !important;
    }

    .print-approvers-section .approval-status {
        font-size: 8pt !important;
    }

    .print-approvers-section .approval-status span {
        color: #000 !important;
        font-weight: normal !important;
    }

    .print-approvers-section .approval-date {
        font-size: 7pt !important;
        color: #000 !important;
    }

    /* Force all colors to black for print */
    .print-approvers-section .action-approved,
    .print-approvers-section .action-rejected,
    .print-approvers-section .action-query,
    .print-approvers-section .action-object,
    .print-approvers-section .action-reply,
    .print-approvers-section .action-pending {
        color: #000 !important;
    }

    /* Images */
    img {
        max-width: 100% !important;
        height: auto !important;
        page-break-inside: avoid !important;
    }

    /* Links - show URL in print */
    a[href]:after {
        content: " (" attr(href) ")" !important;
        font-size: 8pt !important;
        font-style: italic !important;
    }

    /* Remove all backgrounds except specific ones */
    * {
        background: transparent !important;
        box-shadow: none !important;
        text-shadow: none !important;
    }

    /* Restore specific backgrounds */
    .print-section-header,
    .print-attachments-table th,
    .memo-details-table td:first-child {
        background: #f0f0f0 !important;
    }

    .print-optimized-container,
    .print-section-content,
    .print-attachments-table td,
    .approval-box {
        background: white !important;
    }
}
